<?php

namespace Domain\MinisterioSaude\Services\Item\Input;

class ConsultarItensInput
{
    public array $listaItens;

    public function __construct(array $listaItens)
    {
        $this->listaItens = $listaItens;
    }

    public static function fromArray(array $data): ConsultarItensInput
    {
        return new self(
            $data['lista_itens'] ?? [],
        );
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'Data' => [
                'ListaItens' => $this->listaItens
            ],
            'AccessToken' => $accessToken
        ];
    }

}
