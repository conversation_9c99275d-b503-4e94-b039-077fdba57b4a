[2025-09-16 21:00:24] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:00:25] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:00:48] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:00:48] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:01:10] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:01:10] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:13:10] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:24:09] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:24:10] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:35:52] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:35:52] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:47:12] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:47:13] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:03:06] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens?AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","total_itens":4,"codigos_materiais":[1560,785,3500,2560]} 
[2025-09-16 22:03:15] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":4,"protocolo":"1F677D41-62C6-40CB-98A4-1DD947B33DD8"} 
[2025-09-16 22:15:37] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:01] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:17] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:26] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:50] homolog.ERROR: Target [Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface] is not instantiable while building [Domain\MinisterioSaude\Controllers\FaturaFarmanetController, Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService]. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target [Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\Contracts\\MinisterioSaudeLogRepositoryInterface] is not instantiable while building [Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController, Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService]. at C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1089)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(886): Illuminate\\Container\\Container->notInstantiable('Domain\\\\Minister...')
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 22:17:44] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:17:58] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:18:05] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:18:06] homolog.ERROR: Return value of Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\Database\Eloquent\Collection, instance of Domain\MinisterioSaude\Models\MinisterioSaudeLog returned {"exception":"[object] (TypeError(code: 0): Return value of Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\\Database\\Eloquent\\Collection, instance of Domain\\MinisterioSaude\\Models\\MinisterioSaudeLog returned at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:40)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService.php(99): Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository->storeConsultaEndereco(Array, Array, true)
#1 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(93): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService->consultarEndereco(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\Input\\ConsultaEnderecoInput))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarEndereco(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarEnderecoRequest))
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarEndere...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarEndere...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#36 {main}
"} 
[2025-09-16 22:19:31] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:19:34] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:19:38] homolog.ERROR: Return value of Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\Database\Eloquent\Collection, instance of Domain\MinisterioSaude\Models\MinisterioSaudeLog returned {"exception":"[object] (TypeError(code: 0): Return value of Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\\Database\\Eloquent\\Collection, instance of Domain\\MinisterioSaude\\Models\\MinisterioSaudeLog returned at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:40)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService.php(99): Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository->storeConsultaEndereco(Array, Array, true)
#1 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(93): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService->consultarEndereco(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\Input\\ConsultaEnderecoInput))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarEndereco(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarEnderecoRequest))
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarEndere...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarEndere...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#36 {main}
"} 
[2025-09-16 22:19:39] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:19:44] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 23:02:30] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-16 23:02:31] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":0,"protocolo":"89B83395-CED6-4791-94D1-03F94D2F4ABD"} 
