<?php

namespace Domain\MinisterioSaude\Providers;

use Illuminate\Support\ServiceProvider;
use Domain\MinisterioSaude\Services\EnderecoLocalService;
use Domain\MinisterioSaude\Services\GeolocalizacaoService;
use Domain\MinisterioSaude\Services\MinisterioSaudeApiService;
use Domain\MinisterioSaude\Services\StatusOperadorService;
use Domain\MinisterioSaude\Repositories\EnderecoLocalRepository;
use Domain\MinisterioSaude\Repositories\EnderecoLocalRepositoryInterface;

class MinisterioSaudeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Registro dos repositories
        $this->app->bind(
            EnderecoLocalRepositoryInterface::class,
            EnderecoLocalRepository::class
        );

        // Registro dos services como singletons
        $this->app->singleton(MinisterioSaudeApiService::class);
        $this->app->singleton(StatusOperadorService::class);
        $this->app->singleton(GeolocalizacaoService::class);
        $this->app->singleton(EnderecoLocalService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // O arquivo de configuração já existe em config/ministerio_saude.php
        // Não precisa fazer merge adicional
    }
}
