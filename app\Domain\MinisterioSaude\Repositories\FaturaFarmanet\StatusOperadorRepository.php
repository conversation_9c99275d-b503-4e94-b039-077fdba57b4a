<?php

namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;

use Domain\MinisterioSaude\Models\StatusOperador;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;


class StatusOperadorRepository extends BaseAbastractRepository implements StatusOperadorRepositoryInterface
{
    private $model;

    public function __construct(StatusOperador $model)
    {
        parent::__construct($model);
    }

    public function list(Request $filtros): Collection
    {
        return $this->model->where(column: function ($query) use ($filtros): void {
            if ($filtros->has('appname') && !empty($filtros->appname)) {
                $query->where('exec', $filtros->appname);
            }
        })->get();
    }
}
