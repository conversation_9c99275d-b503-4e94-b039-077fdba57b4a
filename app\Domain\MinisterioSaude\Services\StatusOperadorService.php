<?php

namespace Domain\MinisterioSaude\Services;

use Domain\MinisterioSaude\Models\StatusOperador;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class StatusOperadorService
{
    private $client;
    private $baseUrl;
    
    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
    }

    /**
     * Criar novo status tanto localmente quanto na API
     */
    public function criarStatus($idOrigem, $nomeStatus, $descricaoStatus)
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            // Verificar se já existe
            $statusExistente = StatusOperador::where('id_origem', $idOrigem)
                ->orWhere('nome_status', $nomeStatus)
                ->first();

            if ($statusExistente) {
                $errors = [];
                if ($statusExistente->id_origem == $idOrigem) { // Mudança: usar == em vez de ===
                    $errors[] = 'IdOrigem já conta na base';
                }
                if ($statusExistente->nome_status == $nomeStatus) { // Mudança: usar == em vez de ===
                    $errors[] = 'NomeStatus já conta na base';
                }

                DB::connection('ministerio_saude_sp')->rollBack();
                
                // Garantir que sempre haja uma mensagem
                $message = !empty($errors) ? implode(' e ', $errors) : 'Registro já existe na base';
                
                return [
                    'success' => false,
                    'message' => $message
                ];
            }

            $apiResponse = null;
            
            // Fazer chamada HTTP real para a API do Ministério
            try {
                $url = $this->baseUrl . config('ministerio_saude.endpoints.inserir_status_op');
                
                $requestData = [
                    'Data' => [
                        'IdOrigem' => $idOrigem,
                        'NomeStatus' => $nomeStatus,
                        'DescricaoStatus' => $descricaoStatus,
                    ],
                    'AccessToken' => config('ministerio_saude.api.access_token')
                ];
                
                Log::info('StatusOperadorService - Chamando API real do Ministério', [
                    'url' => $url,
                    'data' => collect($requestData)->except(['AccessToken'])->toArray() // Log sem o token
                ]);
                
                $response = $this->client->post($url, [
                    'json' => $requestData,
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Accept' => 'application/json',
                    ]
                ]);
                
                $responseBody = json_decode($response->getBody()->getContents(), true);
                
                $apiResponse = [
                    'success' => $response->getStatusCode() === 200,
                    'data' => $responseBody,
                    'message' => $responseBody['Message'] ?? 'Status inserido via API'
                ];
                
                Log::info('StatusOperadorService - Resposta da API real recebida', [
                    'status_code' => $response->getStatusCode(),
                    'response' => $responseBody
                ]);
                
            } catch (RequestException $e) {
                $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
                
                Log::error('StatusOperadorService - Erro na API externa', [
                    'error' => $errorMessage,
                    'url' => $url ?? 'URL não definida',
                    'data' => $requestData ?? []
                ]);
                
                // Não usar API mockada - falhar se API real não funcionar
                DB::connection('ministerio_saude_sp')->rollBack();
                return [
                    'success' => false,
                    'message' => $errorMessage
                ];
            }

            // Se a API respondeu com sucesso ou estamos em desenvolvimento, criar localmente
            $status = StatusOperador::create([
                'id_origem' => $idOrigem,
                'nome_status' => $nomeStatus,
                'descricao_status' => $descricaoStatus,
                'flag_registro' => true
            ]);

            DB::connection('ministerio_saude_sp')->commit();

            Log::info('StatusOperadorService - Status criado com sucesso', [
                'status_id' => $status->id,
                'id_origem' => $idOrigem,
                'nome_status' => $nomeStatus,
                'api_externa_usada' => true
            ]);

            return [
                'success' => true,
                'data' => $status,
                'message' => 'Status incluído com Sucesso'
            ];

        } catch (\Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            
            Log::error('StatusOperadorService - Erro ao criar status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno ao criar status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Consultar status via API real do Ministério
     */
    public function consultarStatus($idStatus = null, $idOrigem = null, $nomeStatus = null)
    {
        try {
            // Fazer chamada HTTP real para a API do Ministério (GET com query parameters)
            $url = $this->baseUrl . config('ministerio_saude.endpoints.consultar_status_op');
            
            $queryParams = [
                'AccessToken' => config('ministerio_saude.api.access_token')
            ];
            
            // Adicionar parâmetros não nulos
            if ($idStatus !== null) {
                $queryParams['IdStatus'] = $idStatus;
            }
            if ($idOrigem !== null) {
                $queryParams['IdOrigem'] = $idOrigem;
            }
            if ($nomeStatus !== null) {
                $queryParams['NomeStatus'] = $nomeStatus;
            }
            
            Log::info('StatusOperadorService - Consultando status via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);
            
            $response = $this->client->get($url, [
                'query' => $queryParams,
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            
            $responseBody = json_decode($response->getBody()->getContents(), true);
            
            Log::info('StatusOperadorService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'total_records' => count($responseBody['Data'] ?? [])
            ]);
            
            return [
                'success' => $response->getStatusCode() === 200,
                'data' => $responseBody,
                'message' => $responseBody['Message'] ?? 'Consulta realizada com sucesso'
            ];
            
        } catch (RequestException $e) {
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('StatusOperadorService - Erro na consulta da API externa', [
                'error' => $errorMessage,
                'url' => $url ?? 'URL não definida'
            ]);
            
            return [
                'success' => false,
                'message' => $errorMessage
            ];
            
        } catch (\Exception $e) {
            Log::error('StatusOperadorService - Erro ao consultar status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno ao consultar status: ' . $e->getMessage()
            ];
        }
    }
}
