<?php

namespace Domain\MinisterioSaude\Services;

use Domain\MinisterioSaude\DTOs\ConsultarEnderecoDTO;
use Domain\MinisterioSaude\DTOs\EnderecoResponseDTO;
use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
use Domain\MinisterioSaude\Repositories\EnderecoLocalRepositoryInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class EnderecoLocalService
{
    private EnderecoLocalRepositoryInterface $repository;
    private GeolocalizacaoService $geoService;
    private Client $client;
    private string $baseUrl;

    public function __construct(
        EnderecoLocalRepositoryInterface $repository,
        GeolocalizacaoService $geoService
    ) {
        $this->repository = $repository;
        $this->geoService = $geoService;
        
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
    }

    /**
     * Consultar endereço na API do Ministério da Saúde
     */
    public function consultarEndereco(string $idGestor, string $idLocal): array
    {
        try {
            $dto = new ConsultarEnderecoDTO(
                config('ministerio_saude.api.access_token'),
                $idGestor,
                $idLocal
            );

            if (!$dto->isValid()) {
                return [
                    'success' => false,
                    'message' => 'Dados de entrada inválidos: ' . implode(', ', $dto->validate()),
                    'data' => null
                ];
            }

            $startTime = microtime(true);
            
            // NOTA: A API de endereço do Ministério pode não estar disponível
            // Fazer chamada HTTP real para a API do Ministério usando GET
            $url = $this->baseUrl . config('ministerio_saude.endpoints.consultar_endereco_local');
            
            $queryParams = [
                'IdGestor' => $dto->idGestor,
                'IdLocal' => $dto->idLocal,
                'AccessToken' => config('ministerio_saude.api.access_token')
            ];
            
            Log::info('EnderecoLocalService - Chamando API real do Ministério', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            try {
                $response = $this->client->get($url, [
                    'query' => $queryParams,
                    'headers' => [
                        'Accept' => 'application/json',
                    ]
                ]);
            } catch (RequestException $e) {
                $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
                
                if ($statusCode === 404) {
                    Log::warning('EnderecoLocalService - API de endereço não encontrada', [
                        'url' => $url,
                        'status_code' => $statusCode,
                        'message' => 'Endpoint de endereço não está disponível na API do Ministério'
                    ]);
                    
                    return [
                        'success' => false,
                        'message' => 'API de consulta de endereços não está disponível no Ministério da Saúde SP. Endpoint: ' . $url,
                        'data' => null,
                        'error_code' => 'ENDPOINT_NOT_FOUND'
                    ];
                }
                
                // Re-throw para outros tipos de erro
                throw $e;
            }
            
            $responseBody = json_decode($response->getBody()->getContents(), true);
            $endTime = microtime(true);

            $tempoResposta = round(($endTime - $startTime) * 1000);

            if ($response->getStatusCode() === 200 && !empty($responseBody['Data'])) {
                $enderecoDTO = new EnderecoResponseDTO($responseBody['Data'][0] ?? []);
                
                Log::info('EnderecoLocalService - Resposta da API real recebida', [
                    'status_code' => $response->getStatusCode(),
                    'endereco_encontrado' => !empty($responseBody['Data'][0])
                ]);
                
                // Remover geolocalização por enquanto - focar apenas na API real
                // if (config('ministerio_saude.geolocalizacao.enabled')) {
                //     dispatch(function() use ($enderecoDTO) {
                //         $this->geoService->processarGeolocalizacao($enderecoDTO);
                //     })->afterResponse();
                // }

                // Log de sucesso
                MinisterioSaudeLog::consultarEndereco(
                    $dto->toQueryParams(),
                    array_merge($responseBody, ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
                    true
                );

                return [
                    'success' => true,
                    'message' => 'Endereço consultado com sucesso via API real',
                    'data' => [
                        'endereco_api' => $enderecoDTO->toArray(),
                        'tempo_resposta_ms' => $tempoResposta,
                        'fonte' => 'API Real do Ministério da Saúde'
                    ]
                ];
            } else {
                // Log de erro
                MinisterioSaudeLog::consultarEndereco(
                    $dto->toQueryParams(),
                    array_merge($responseBody ?? [], ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
                    false,
                    $responseBody['Message'] ?? 'Endereço não encontrado'
                );

                return [
                    'success' => false,
                    'message' => $responseBody['Message'] ?? 'Endereço não encontrado na API',
                    'data' => $responseBody
                ];
            }

        } catch (RequestException $e) {
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('EnderecoLocalService - Erro na API externa', [
                'error' => $errorMessage,
                'url' => $url ?? 'URL não definida'
            ]);
            
            return [
                'success' => false,
                'message' => $errorMessage
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@consultarEndereco - Erro', [
                'id_gestor' => $idGestor,
                'id_local' => $idLocal,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Log de erro
            MinisterioSaudeLog::consultarEndereco(
                ['IdGestor' => $idGestor, 'IdLocal' => $idLocal],
                null,
                false,
                $e->getMessage()
            );

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Listar endereços locais
     */
    public function listarEnderecos(): array
    {
        try {
            $enderecos = $this->repository->findAtivos();

            return [
                'success' => true,
                'message' => 'Endereços listados com sucesso',
                'data' => $enderecos->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@listarEnderecos - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Obter endereço por ID
     */
    public function obterEndereco(string $idGestor, string $idLocal): array
    {
        try {
            $endereco = $this->repository->findByGestorELocal($idGestor, $idLocal);

            if (!$endereco) {
                return [
                    'success' => false,
                    'message' => 'Endereço não encontrado',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => 'Endereço encontrado',
                'data' => $endereco->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@obterEndereco - Erro', [
                'id_gestor' => $idGestor,
                'id_local' => $idLocal,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Sincronizar endereço específico com a API
     */
    public function sincronizarEndereco(string $idGestor, string $idLocal): array
    {
        try {
            $resultado = $this->consultarEndereco($idGestor, $idLocal);

            if ($resultado['success']) {
                return [
                    'success' => true,
                    'message' => 'Endereço sincronizado com sucesso',
                    'data' => $resultado['data']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Falha na sincronização: ' . $resultado['message'],
                    'data' => $resultado['data']
                ];
            }

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@sincronizarEndereco - Erro', [
                'id_gestor' => $idGestor,
                'id_local' => $idLocal,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Processar geolocalização para endereços sem coordenadas
     */
    public function processarGeolocalizacaoLote(int $limite = null): array
    {
        try {
            $limite = $limite ?? config('ministerio_saude.geolocalizacao.batch_limit', 50);
            
            if (!config('ministerio_saude.geolocalizacao.enabled')) {
                return [
                    'success' => false,
                    'message' => 'Geolocalização está desabilitada',
                    'data' => null
                ];
            }

            $resultados = $this->geoService->processarLoteGeolocalizacao($limite);

            return [
                'success' => true,
                'message' => 'Processamento de geolocalização concluído',
                'data' => $resultados
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@processarGeolocalizacaoLote - Erro', [
                'limite' => $limite,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Obter estatísticas dos endereços
     */
    public function obterEstatisticas(): array
    {
        try {
            $total = $this->repository->findAll()->count();
            $ativos = $this->repository->findAtivos()->count();
            $semGeolocalizacao = $this->repository->findSemGeolocalizacao()->count();
            $comGeolocalizacao = $ativos - $semGeolocalizacao;

            return [
                'success' => true,
                'message' => 'Estatísticas obtidas com sucesso',
                'data' => [
                    'total_enderecos' => $total,
                    'enderecos_ativos' => $ativos,
                    'com_geolocalizacao' => $comGeolocalizacao,
                    'sem_geolocalizacao' => $semGeolocalizacao,
                    'percentual_geolocalizados' => $ativos > 0 ? round(($comGeolocalizacao / $ativos) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@obterEstatisticas - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }
}
