<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\FaturaGsnet;
use App\Models\FaturaGsnetItem;
use App\Models\FaturaGsnetStatusControle;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class FaturaGsnetService
{
    private Client $client;
    private string $baseUrl;
    
    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
    }
    /**
     * API 2.1 - Consultar Faturas via API real do Ministério
     * 
     * @param array $data
     * @return array
     */
    public function consultarFaturas(array $data): array
    {
        try {
            $endpoint = config('ministerio_saude.endpoints.consultar_faturas_gsnet');
            $accessToken = config('ministerio_saude.api.access_token');
            
            // Montar query parameters baseado no seu exemplo
            $queryParams = [
                'AccessToken' => $accessToken,
                'IdGestor' => $data['id_gestor']
            ];
            
            // Adicionar datas de emissão se fornecidas
            if (isset($data['data_inicio'])) {
                $queryParams['DtEmissao'] = $data['data_inicio'];
            }
            if (isset($data['data_fim'])) {
                $queryParams['DtEmissaoFim'] = $data['data_fim'];
            }
            
            // Adicionar outros parâmetros opcionais
            if (isset($data['ano_referencia'])) {
                $queryParams['AnoReferencia'] = $data['ano_referencia'];
            }
            if (isset($data['mes_referencia'])) {
                $queryParams['MesReferencia'] = $data['mes_referencia'];
            }
            if (isset($data['codigo_programa'])) {
                $queryParams['CodigoPrograma'] = $data['codigo_programa'];
            }
            if (isset($data['status'])) {
                $queryParams['Status'] = $data['status'];
            }
            
            $url = $this->baseUrl . $endpoint . '?' . http_build_query($queryParams);
            
            Log::info('FaturaGsnetService - Consultando faturas via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);
            
            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            
            $responseBody = json_decode($response->getBody()->getContents(), true);
            
            Log::info('FaturaGsnetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null,
                'total_faturas' => count($responseBody['Data'] ?? [])
            ]);
            
            return [
                'success' => true,
                'data' => $responseBody,
                'message' => 'Consulta de faturas realizada com sucesso via API real do Ministério.',
                'total_faturas' => count($responseBody['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('FaturaGsnetService - Erro na requisição para o Ministério', [
                'message' => $e->getMessage(),
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $data
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('FaturaGsnetService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao consultar faturas: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }
    
    /**
            ]);
            
            return [
                'success' => false,
                'message' => $errorMessage
            ];

            $faturas = $query->orderBy('data_criacao', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            // Formatação para a resposta da API
            $response = [
                'Data' => [],
                'Meta' => [
                    'TotalItens' => $faturas->total(),
                    'PaginaAtual' => $faturas->currentPage(),
                    'ItensPorPagina' => $faturas->perPage(),
                    'TotalPaginas' => $faturas->lastPage()
                ]
            ];

            foreach ($faturas->items() as $fatura) {
                $faturaData = [
                    'ProtocoloIdGsnet' => $fatura->protocolo_id_gsnet,
                    'IdGestor' => $fatura->id_gestor,
                    'NrDocumento' => $fatura->nr_documento,
                    'DescricaoDocumento' => $fatura->descricao_documento,
                    'NrProcesso' => $fatura->nr_processo,
                    'DescricaoProcesso' => $fatura->descricao_processo,
                    'ValorTotal' => (float) $fatura->valor_total,
                    'LocalOrigem' => [
                        'Id' => $fatura->local_origem_id,
                        'Codigo' => $fatura->local_origem_codigo,
                        'Descricao' => $fatura->local_origem_descricao
                    ],
                    'LocalDestino' => [
                        'Id' => $fatura->local_destino_id,
                        'Codigo' => $fatura->local_destino_codigo,
                        'Descricao' => $fatura->local_destino_descricao
                    ],
                    'StatusAtual' => $fatura->status_atual,
                    'DataCriacao' => $fatura->data_criacao->format('Y-m-d H:i:s'),
                    'DataUltimaAtualizacao' => $fatura->data_ultima_atualizacao->format('Y-m-d H:i:s'),
                    'Itens' => []
                ];

                // Itens da fatura
                foreach ($fatura->itens as $item) {
                    $faturaData['Itens'][] = [
                        'CodigoMaterial' => (int) $item->codigo_material,
                        'CodigoSiafisico' => (int) $item->codigo_siafisico,
                        'NomeMaterial' => $item->nome_material,
                        'QuantidadeMaterial' => (float) $item->quantidade_material,
                        'PrecoMedio' => (float) $item->preco_medio
                    ];
                }

                $response['Data'][] = $faturaData;
            }

            return $response;

        } catch (\Exception $e) {
            Log::error('FaturaGsnetService@consultarFaturas - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * API 2.2 - Atualizar Status da Fatura
     * 
     * @param array $data
     * @return array
     */
    public function atualizarStatusFatura(array $data): array
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            // Detectar formato e extrair dados
            if (isset($data['Data'])) {
                // Formato original
                $protocoloId = $data['Data']['ProtocoloIdGsnet'];
                $statusCodigo = $data['Data']['StatusCodigo'];
                $statusDescricao = $data['Data']['StatusDescricao'];
                $observacao = $data['Data']['Observacao'] ?? null;
                $dataStatus = $data['Data']['DataStatus'];
            } else {
                // Formato novo - usar numero_fatura como protocolo
                $protocoloId = $data['numero_fatura'];
                $statusCodigo = (string) $data['status_fatura'];
                $statusDescricao = "Status {$data['status_fatura']} - Atualizado via API";
                $observacao = $data['observacoes'] ?? null;
                $dataStatus = $data['data_atualizacao'];
            }
            
            // Buscar a fatura
            $fatura = FaturaGsnet::where('protocolo_id_gsnet', $protocoloId)
                ->where('ativo', true)
                ->first();

            if (!$fatura) {
                // Se não encontrar, criar uma nova fatura para teste (modo desenvolvimento)
                $fatura = FaturaGsnet::create([
                    'protocolo_id_gsnet' => $protocoloId,
                    'nr_documento' => $protocoloId, // Usar o protocolo como número do documento
                    'id_gestor' => $data['id_gestor'] ?? '0',
                    'status_atual' => $statusCodigo,
                    'data_criacao' => now(),
                    'data_ultima_atualizacao' => Carbon::parse($dataStatus),
                    'ativo' => true
                ]);
                
                $mensagemBase = 'Nova fatura criada e status definido com sucesso';
            } else {
                // Atualizar status atual da fatura existente
                $fatura->status_atual = $statusCodigo;
                $fatura->data_ultima_atualizacao = Carbon::parse($dataStatus);
                $fatura->save();
                
                $mensagemBase = 'Status da fatura atualizado com sucesso';
            }

            // Registrar no controle de status
            FaturaGsnetStatusControle::create([
                'fatura_id' => $fatura->id,
                'protocolo_id_gsnet' => $protocoloId,
                'status_codigo' => $statusCodigo,
                'status_descricao' => $statusDescricao,
                'observacao' => $observacao,
                'data_status' => Carbon::parse($dataStatus),
                'enviado_ministerio' => false
            ]);

            DB::connection('ministerio_saude_sp')->commit();

            return [
                'success' => true,
                'message' => $mensagemBase . ' (modo desenvolvimento)'
            ];

        } catch (\Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            
            Log::error('FaturaGsnetService@atualizarStatusFatura - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao atualizar status da fatura: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Buscar fatura por protocolo
     * 
     * @param string $protocoloId
     * @return FaturaGsnet|null
     */
    public function buscarPorProtocolo(string $protocoloId): ?FaturaGsnet
    {
        return FaturaGsnet::with(['itens', 'statusControle'])
            ->where('protocolo_id_gsnet', $protocoloId)
            ->where('ativo', true)
            ->first();
    }

    /**
     * Criar nova fatura
     * 
     * @param array $dadosFatura
     * @param array $itens
     * @return FaturaGsnet
     */
    public function criarFatura(array $dadosFatura, array $itens = []): FaturaGsnet
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            $fatura = FaturaGsnet::create($dadosFatura);

            // Criar itens se fornecidos
            foreach ($itens as $item) {
                $item['fatura_id'] = $fatura->id;
                $item['protocolo_id_gsnet'] = $fatura->protocolo_id_gsnet;
                FaturaGsnetItem::create($item);
            }

            DB::connection('ministerio_saude_sp')->commit();

            return $fatura->load(['itens', 'statusControle']);

        } catch (\Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            throw $e;
        }
    }
}
