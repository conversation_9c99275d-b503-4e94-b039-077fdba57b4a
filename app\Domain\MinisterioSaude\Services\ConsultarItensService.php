<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\ItemMaterial;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ConsultarItensService
{
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.items.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.items.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
    }

    /**
     * Consulta itens no sistema GSNET do Ministério da Saúde
     *
     * @param array $data
     * @return array
     */
    public function consultarItens(array $data): array
    {
        try {
            $endpoint = config('ministerio_saude.items.endpoints.consultar_itens');
            $url = $this->baseUrl . $endpoint . '?AccessToken=' . $data['AccessToken'];
            
            Log::info('ConsultarItensService - Iniciando consulta de itens via API real', [
                'url' => $url,
                'total_itens' => count($data['Data']['ListaItens']),
                'codigos_materiais' => array_column($data['Data']['ListaItens'], 'CodigoMaterial')
            ]);

            // Preparar payload para o Ministério
            $payload = [
                'Data' => [
                    'ListaItens' => $data['Data']['ListaItens']
                ],
                'AccessToken' => $data['AccessToken']
            ];

            // Header customizado necessário (usar o primeiro CodigoMaterial como exemplo)
            $primeiroCodigoMaterial = $data['Data']['ListaItens'][0]['CodigoMaterial'] ?? '1160';

            $response = $this->client->put($url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'CodigoMaterial' => (string)$primeiroCodigoMaterial, // Header customizado necessário
                ],
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            
            Log::info('ConsultarItensService - Resposta recebida do Ministério', [
                'status_code' => $response->getStatusCode(),
                'total_itens_retornados' => count($responseData['Data'] ?? []),
                'protocolo' => $responseData['Protocolo'] ?? null
            ]);

            return [
                'success' => true,
                'data' => $responseData,
                'message' => 'Consulta de itens realizada com sucesso via API real do Ministério.',
                'total_itens' => count($responseData['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('ConsultarItensService - Erro na requisição para o Ministério', [
                'message' => $e->getMessage(),
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $data
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('ConsultarItensService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao consultar itens: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Processa e armazena os itens recebidos do Ministério
     *
     * @param array $itens
     * @return void
     */
    private function processarEArmazenarItens(array $itens): void
    {
        foreach ($itens as $item) {
            try {
                ItemMaterial::updateOrCreate(
                    ['codigo_material' => $item['CodigoMaterial']],
                    [
                        'codigo_material' => $item['CodigoMaterial'],
                        'nome_material' => $item['NomeMaterial'] ?? null,
                        'nome_descricao_tecnica' => $item['NomeDescricaoTecnica'] ?? null,
                        'nome_unidade_medida' => $item['NomeUnidadeMedida'] ?? null,
                        'st_registro' => $item['StRegistro'] ?? null,
                        'codigo_siafisico' => $item['CodigoSiafisico'] ?? null,
                        'data_ultima_consulta' => Carbon::now(),
                        'origem' => 'ministerio_saude_sp'
                    ]
                );
            } catch (\Exception $e) {
                Log::warning('ConsultarItensService - Erro ao armazenar item', [
                    'codigo_material' => $item['CodigoMaterial'] ?? 'indefinido',
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
