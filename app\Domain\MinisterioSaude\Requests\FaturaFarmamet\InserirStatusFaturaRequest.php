<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Illuminate\Foundation\Http\FormRequest;

class InserirStatusFaturaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'data.id_origem' => 'required|string|max:11',
            'data.nome_status' => 'required|string|max:40',
            'data.descricao_status' => 'required|string|max:240',
            'system_code' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'data.id_origem.required' => 'IdOrigem não informado',
            'data.id_origem.max' => 'IdOrigem deve ter no máximo 11 caracteres',
            'data.nome_status.required' => 'NomeStatus não informado',
            'data.nome_status.max' => 'NomeStatus deve ter no máximo 40 caracteres',
            'data.descricao_status.required' => 'DescricaoStatus não informado',
            'data.descricao_status.max' => 'DescricaoStatus deve ter no máximo 240 caracteres',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'data.id_origem' => 'ID de Origem',
            'data.nome_status' => 'Nome do Status',
            'data.descricao_status' => 'Descrição do Status',
            'system_code' => 'Código do Sistema'
        ];
    }
}
