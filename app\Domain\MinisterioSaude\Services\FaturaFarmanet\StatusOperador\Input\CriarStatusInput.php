<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;

use Illuminate\Http\Request;

class CriarStatusInput{
    
    public string $idOrigem;
    public string $nomeStatus;
    public string $descricaoStatus;
    public ?string $systemCode = null;

    public function __construct(string $idOrigem, string $nomeStatus, string $descricaoStatus, ?string $systemCode = null){
        $this->idOrigem = $idOrigem;
        $this->nomeStatus = $nomeStatus;
        $this->descricaoStatus = $descricaoStatus;
        $this->systemCode = $systemCode;
    }

    public function toArray(): array {
        return [
            'data' => [
                'id_origem' => $this->idOrigem,
                'nome_status' => $this->nomeStatus,
                'descricao_status' => $this->descricaoStatus
            ],
            'system_code' => $this->systemCode
        ];
    }
    
    public static function fromRequest(Request $request): CriarStatusInput{
        return new self(
            idOrigem: $request->input(key: 'data.id_origem'),
            nomeStatus: $request->input(key: 'data.nome_status'),
            descricaoStatus: $request->input(key: 'data.descricao_status'),
            systemCode: $request->input(key: 'system_code')
        );
    }
}


