<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;

class CriarStatusInput{
    
    public function __construct(        
        public string $idOrigem,
        public string $nomeStatus,        
        public string $descricaoStatus,
        public string $systemCode = null    
    ) {}    

    public static function fromArray(array $data): self {        
        return new self(
            idOrigem: $data['data']['id_origem'],            
            nomeStatus: $data['data']['nome_status'],            
            descricaoStatus: $data['data']['descricao_status'],            
            accessToken: $data['access_token'] ?? '',            
            systemCode: $data['system_code'] ?? null        
        );    
    }    

    public function toArray(): array {        
        return [            
            'data' => [                
                'id_origem' => $this->id_origem,                
                'nome_status' => $this->nome_status,                
                'descricao_status' => $this->descricaoStatus            
            ],            
            'AccessToken' => $this->accessToken,            
            'SystemCode' => $this->systemCode        
        ];    
    }
}    



















