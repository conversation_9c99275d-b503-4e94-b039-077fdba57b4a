<?php
namespace Domain\MinisterioSaude\Repositories\Common;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

interface BaseRepositoryInterface
{
    public function list(Array $filtros):Collection;
    public function get(int $id):Model;
    public function getWhere(callable $conditions = null): Collection;
    public function getOneWhere(callable $conditions = null): ?Model;
    public function findBy(array $conditions): Model;
    public function store(Array $request):Collection;
    public function storeArray(Array $data):Collection;
    public function storeSingleData(Array $data):Collection;
    public function updateOrStore(Array $attributes, ?Array $values):Collection;
    public function update(Array $request,int $id): bool;
    public function updateArray(Array $data,int $id):bool;
    public function destroy(int $id): bool;
    public function searchByCustomField(string $field,string $value):Collection;
}
