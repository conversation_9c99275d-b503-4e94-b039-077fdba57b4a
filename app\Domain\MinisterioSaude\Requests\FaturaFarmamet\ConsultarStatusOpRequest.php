<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Illuminate\Foundation\Http\FormRequest;

class ConsultarStatusOpRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_status' => 'nullable|string|max:40',
            'id_origem' => 'nullable|string|max:40',
            'nome_status' => 'nullable|string|max:40',
        ];
    }

    public function messages(): array
    {
        return [
            'id_status.max' => 'id_status deve ter no máximo 40 caracteres',
            'id_origem.max' => 'id_origem deve ter no máximo 40 caracteres',
            'nome_status.max' => 'nome_status deve ter no máximo 40 caracteres'
        ];
    }

    public function attributes(): array
    {
        return [
            'id_status' => 'ID do Status',
            'id_origem' => 'ID de Origem',
            'nome_status' => 'Nome do Status'
        ];
    }
}
