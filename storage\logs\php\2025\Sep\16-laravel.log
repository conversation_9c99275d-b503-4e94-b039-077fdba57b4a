[2025-09-16 08:42:47] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:43:47] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:44:47] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:45:47] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:46:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:47:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:48:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:49:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:50:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:51:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:52:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:53:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:54:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:55:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:56:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:57:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:58:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 08:59:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:00:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:01:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:02:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:03:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:04:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:05:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:06:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:07:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:08:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:09:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:10:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:11:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:12:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:13:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:14:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:15:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:16:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:17:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:18:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:19:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:20:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:21:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:22:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:23:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:24:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:25:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:26:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:27:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:28:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:29:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:30:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:31:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:32:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:33:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:34:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:35:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:36:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:37:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:38:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:39:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:40:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:41:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:42:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:43:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:44:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:45:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:46:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:47:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:48:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:49:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:50:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:51:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:52:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:53:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:54:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:55:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:56:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:57:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:58:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 09:59:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:00:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:01:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:02:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:03:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:04:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:05:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:06:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:07:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:08:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:09:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:10:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:11:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:12:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:13:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:14:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:15:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:16:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:17:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:18:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:19:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:20:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:21:34] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:22:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:23:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:24:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:25:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:26:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:27:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:28:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:29:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:30:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:31:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:32:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:33:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:34:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:35:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:36:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:37:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:38:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:39:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:40:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:41:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:42:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:43:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:44:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:45:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:46:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:47:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:48:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:49:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:50:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:51:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:52:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:53:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:54:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:55:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:56:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:57:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:58:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 10:59:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:00:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:01:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:02:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:03:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:04:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:05:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:06:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:07:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:08:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:09:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:10:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:11:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:12:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:13:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:14:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:15:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:16:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:17:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:18:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:19:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:20:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:21:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:22:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:23:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:24:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:25:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:26:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:27:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:28:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:29:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:30:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:31:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:32:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:33:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:34:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:35:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:36:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:37:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:38:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:39:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:40:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:41:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:42:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:43:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:44:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:45:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:46:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:47:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:48:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:49:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:50:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:51:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:52:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:53:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:54:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:55:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:56:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:57:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:58:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 11:59:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:00:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:01:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:02:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:03:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:04:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:05:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:06:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:07:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:08:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:09:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:10:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:11:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:12:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:13:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:14:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:15:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:16:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:17:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:18:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:19:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:20:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:21:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:22:35] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:23:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:24:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:25:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:26:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:27:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:28:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:29:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:30:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:31:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:32:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:33:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:34:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:35:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:36:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:37:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:38:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:39:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:40:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:41:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:42:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:43:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:44:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:45:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:46:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:47:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:48:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:49:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:50:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:51:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:52:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:53:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:54:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:55:44] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:56:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:57:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:58:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 12:59:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:00:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:01:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:02:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:03:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:04:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:05:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:06:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:07:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:08:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:09:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:10:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:11:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:12:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:13:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:14:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:15:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:16:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:17:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:18:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:19:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:20:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:21:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:22:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:23:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:24:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:25:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:26:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:27:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:28:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:29:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:30:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:31:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:32:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:33:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:34:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:35:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:36:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:37:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:38:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:39:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:40:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:41:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:42:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:43:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:44:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:45:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:46:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:47:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:48:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:49:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:50:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:51:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:52:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:53:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:54:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:55:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:56:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:57:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:58:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 13:59:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:00:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:01:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:02:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:03:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:04:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:05:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:06:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:07:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:08:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:09:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:10:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:11:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:12:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:13:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:14:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:15:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:16:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:17:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:18:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:19:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:20:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:21:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:22:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:23:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:24:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:25:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:26:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:27:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:28:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:29:36] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:30:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:31:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:32:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:33:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:34:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:35:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:36:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:37:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:38:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:39:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:40:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:41:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:42:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:43:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:44:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:45:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:46:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:47:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:48:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:49:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:50:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:51:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:52:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:53:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:54:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:55:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:56:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:57:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:58:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 14:59:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:00:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:01:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:02:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:03:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:04:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:05:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:06:23] homolog.INFO: StatusOperadorService - Consultando status via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp","params":[]} 
[2025-09-16 15:06:26] homolog.ERROR: StatusOperadorService - Erro na consulta da API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp"} 
[2025-09-16 15:06:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:07:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:08:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:09:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:10:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:11:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:12:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:13:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:14:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:15:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:16:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:17:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:18:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:19:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:20:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:21:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:22:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:23:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:24:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:25:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:26:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 15:27:37] homolog.ERROR: syntax error, unexpected '->' (T_OBJECT_OPERATOR) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected '->' (T_OBJECT_OPERATOR) at C:\\Dev\\IBL\\DocsAPI\\app\\App\\Core\\Models\\ItemMaterial.php:123)
[stacktrace]
#0 {main}
"} 
[2025-09-16 16:55:45] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 16:55:51] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 16:56:02] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 16:58:20] homolog.ERROR: syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:21)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 16:58:41] homolog.ERROR: syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:21)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 16:59:03] homolog.ERROR: syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:21)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 16:59:05] homolog.ERROR: syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:21)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 16:59:07] homolog.INFO: ObterPrecoMedioItensController - Iniciando consulta de preços médios {"request_data":{"codigo_programa":5,"ano_referencia":2024,"mes_referencia":11,"estado_origem":"SP","access_token":"7fe9ba49-6ce4-4773-b7a6-f6e46a12565b"}} 
[2025-09-16 16:59:07] homolog.INFO: ObterPrecoMedioItensService - Iniciando consulta {"url":"https://operadorgsnethml.saude.sp.gov.br/ObterPrecoMedioItens","params":{"codigo_programa":5,"ano_referencia":2024,"mes_referencia":11,"estado_origem":"SP","access_token":"7fe9ba49-6ce4-4773-b7a6-f6e46a12565b"}} 
[2025-09-16 16:59:09] homolog.ERROR: ObterPrecoMedioItensService - Erro na requisição {"message":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/ObterPrecoMedioItens?codigo_programa=5&ano_referencia=2024&mes_referencia=11&estado_origem=SP` resulted in a `404 Not Found` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>404 - File or directory not found.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>404 - File or directory not found.</h2>

  <h3>The resource you are looking for might have been removed, had its name changed, or is temporarily unavailable.</h3>

 </fieldset></div>

</div>

</body>

</html>

","params":{"codigo_programa":5,"ano_referencia":2024,"mes_referencia":11,"estado_origem":"SP","access_token":"7fe9ba49-6ce4-4773-b7a6-f6e46a12565b"}} 
[2025-09-16 16:59:44] homolog.ERROR: syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected 'private' (T_PRIVATE), expecting variable (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:21)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 17:00:32] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 17:05:32] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 17:07:37] homolog.ERROR: syntax error, unexpected '$data' (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected '$data' (T_VARIABLE) at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:42)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#2 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#37 {main}
"} 
[2025-09-16 17:07:49] homolog.ERROR: include(C:\Dev\IBL\DocsAPI\vendor\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Dev\\IBL\\DocsAPI\\vendor\\composer/../../app/Domain/MinisterioSaude/Services/StatusOperadorService.php): failed to open stream: No such file or directory at C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Dev\\\\...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 576, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Dev\\\\IBL\\\\Docs...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Domain\\\\Minister...')
#4 [internal function]: spl_autoload_call('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(292): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(App\\Application), Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
