<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ConsultarFaturasRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id_gestor' => 'required|string|max:50',
            'ano_referencia' => 'required|integer|min:2000|max:2050',
            'mes_referencia' => 'required|integer|min:1|max:12',
            'codigo_programa' => 'required|integer|min:1',
            'access_token' => 'required|string|max:240',
            // Campos opcionais para filtros adicionais
            'data_inicio' => 'nullable|date_format:Y-m-d',
            'data_fim' => 'nullable|date_format:Y-m-d',
            'local_origem_id' => 'nullable|integer',
            'local_destino_id' => 'nullable|integer',
            'status' => 'nullable|string|max:10',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'id_gestor.required' => 'O ID do gestor é obrigatório',
            'id_gestor.string' => 'O ID do gestor deve ser uma string',
            'id_gestor.max' => 'O ID do gestor não pode ter mais de 50 caracteres',
            'ano_referencia.required' => 'O ano de referência é obrigatório',
            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
            'ano_referencia.min' => 'O ano de referência deve ser no mínimo 2000',
            'ano_referencia.max' => 'O ano de referência deve ser no máximo 2050',
            'mes_referencia.required' => 'O mês de referência é obrigatório',
            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
            'mes_referencia.min' => 'O mês de referência deve ser no mínimo 1',
            'mes_referencia.max' => 'O mês de referência deve ser no máximo 12',
            'codigo_programa.required' => 'O código do programa é obrigatório',
            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
            'codigo_programa.min' => 'O código do programa deve ser maior que 0',
            'access_token.required' => 'O token de acesso é obrigatório',
            'access_token.max' => 'O token de acesso não pode ter mais de 240 caracteres',
            'data_inicio.date_format' => 'A data de início deve estar no formato Y-m-d',
            'data_fim.date_format' => 'A data de fim deve estar no formato Y-m-d',
            'local_origem_id.integer' => 'O ID do local de origem deve ser um número inteiro',
            'local_destino_id.integer' => 'O ID do local de destino deve ser um número inteiro',
            'status.string' => 'O status deve ser uma string',
            'status.max' => 'O status não pode ter mais de 10 caracteres',
            'page.integer' => 'A página deve ser um número inteiro',
            'page.min' => 'A página deve ser no mínimo 1',
            'per_page.integer' => 'O número de itens por página deve ser um número inteiro',
            'per_page.min' => 'O número de itens por página deve ser no mínimo 1',
            'per_page.max' => 'O número de itens por página deve ser no máximo 100'
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        $response = response()->json([
            'success' => false,
            'message' => 'Parâmetros de consulta de faturas inválidos',
            'errors' => $validator->errors()
        ], 422);

        throw new \Illuminate\Validation\ValidationException($validator, $response);
    }
}
