<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AtualizarPedidoFarmanetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id_gestor' => 'required|string|max:22',
            'id_pedido_ms' => 'required|string|max:50',
            'status_pedido' => 'required|integer|in:1,2,3,4,5',
            'observacoes' => 'nullable|string|max:1000',
            'data_atualizacao' => 'required|date_format:Y-m-d H:i:s',
            'access_token' => 'required|string|max:40',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'id_gestor.required' => 'O ID do gestor é obrigatório',
            'id_gestor.string' => 'O ID do gestor deve ser uma string',
            'id_gestor.max' => 'O ID do gestor não pode ter mais de 22 caracteres',
            
            'id_pedido_ms.required' => 'O ID do pedido MS é obrigatório',
            'id_pedido_ms.string' => 'O ID do pedido MS deve ser uma string',
            'id_pedido_ms.max' => 'O ID do pedido MS não pode ter mais de 50 caracteres',
            
            'status_pedido.required' => 'O status do pedido é obrigatório',
            'status_pedido.integer' => 'O status do pedido deve ser um número inteiro',
            'status_pedido.in' => 'O status deve ser: 1 (Pendente), 2 (Em Processamento), 3 (Aprovado), 4 (Rejeitado), 5 (Cancelado)',
            
            'observacoes.string' => 'As observações devem ser uma string',
            'observacoes.max' => 'As observações não podem ter mais de 1000 caracteres',
            
            'data_atualizacao.required' => 'A data de atualização é obrigatória',
            'data_atualizacao.date_format' => 'A data de atualização deve estar no formato Y-m-d H:i:s',
            
            'access_token.required' => 'O token de acesso é obrigatório',
            'access_token.string' => 'O token de acesso deve ser uma string',
            'access_token.max' => 'O token de acesso não pode ter mais de 40 caracteres',
        ];
    }
}
