<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Services\ConsultarItensService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ConsultarItensController extends Controller
{
    private ConsultarItensService $consultarItensService;

    public function __construct(ConsultarItensService $consultarItensService)
    {
        $this->consultarItensService = $consultarItensService;
    }

    /**
     * API 1.4 - Consultar Itens do Sistema GSNET
     * 
     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function consultarItens(Request $request): JsonResponse
    {
        try {
            // Validação dos dados de entrada
            $validator = Validator::make($request->all(), [
                'Data' => 'required|array',
                'Data.ListaItens' => 'required|array|min:1|max:100',
                'Data.ListaItens.*.CodigoMaterial' => 'required|numeric|digits_between:1,11',
                'AccessToken' => 'required|string|max:240'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos fornecidos.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validatedData = $validator->validated();

            // Chamar o serviço para consultar os itens
            $result = $this->consultarItensService->consultarItens($validatedData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => $result['message'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'timestamp' => $result['timestamp']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }
}
