<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\InserirStatusFaturaRequest;
use Domain\MinisterioSaude\Requests\ConsultarStatusOpRequest;
use Domain\MinisterioSaude\Requests\ConsultarEnderecoRequest;
use Domain\MinisterioSaude\Requests\ConsultarFaturasRequest;
use Domain\MinisterioSaude\Requests\AtualizarStatusFaturaRequest;
use Domain\MinisterioSaude\Services\StatusOperadorService;
use Domain\MinisterioSaude\Services\EnderecoLocalService;
use Domain\MinisterioSaude\Services\FaturaGsnetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class FaturaFarmanetController extends Controller
{

    private StatusOperadorService $statusService;
    private EnderecoLocalService $enderecoService;

    public function __construct(
        StatusOperadorService $statusService,
        EnderecoLocalService $enderecoService
    ) {
        $this->statusService = $statusService;
        $this->enderecoService = $enderecoService;
    }

    /**
     * API 1.1 - Inserir Status na Fatura
     * 
     * @param InserirStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            $result = $this->statusService->criarStatus(
                $data['data']
            );

            if ($result['success']) {
                return response()->json([
                    'Message' => $result['message']
                ], 200);
            } else {
                return response()->json([
                    'Message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@inserirStatusFatura - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * API 1.2 - Consultar Status do Operador
     * 
     * @param ConsultarStatusOpRequest $request
     * @return JsonResponse
     */
    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->statusService->consultarStatus(
                $validated['IdStatus'] ?? null,
                $validated['IdOrigem'] ?? null,
                $validated['NomeStatus'] ?? null
            );

            if ($result['success']) {
                return response()->json($result['data'], 200);
            } else {
                return response()->json([
                    'Message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@consultarStatusOp - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * API 1.3 - Consultar Endereço Local
     * 
     * @param ConsultarEnderecoRequest $request
     * @return JsonResponse
     */
    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->enderecoService->consultarEndereco(
                $validated['IdGestor'],
                $validated['IdLocal']
            );

            if ($result['success']) {
                return response()->json([
                    'Message' => $result['message'],
                    'Data' => $result['data']
                ], 200);
            } else {
                // Para ResultCode 204, retornar 200 com a mensagem de dados não encontrados
                return response()->json([
                    'Message' => $result['message'],
                    'Data' => $result['data']
                ], 200);
            }

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@consultarEndereco - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * API 2.1 - Consultar Faturas (Recebe)
     * 
     * @param ConsultarFaturasRequest $request
     * @return JsonResponse
     */
    public function consultarFaturas(ConsultarFaturasRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            $faturaService = app(FaturaGsnetService::class);
            $result = $faturaService->consultarFaturas($data);

            return response()->json($result, 200);

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@consultarFaturas - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * API 2.2 - Atualizar Status da Fatura (Envio)
     * 
     * @param AtualizarStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function atualizarStatusFatura(AtualizarStatusFaturaRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            $faturaService = app(\Domain\MinisterioSaude\Services\FaturaGsnetService::class);
            $result = $faturaService->atualizarStatusFatura($data);

            if ($result['success']) {
                return response()->json([
                    'Message' => $result['message']
                ], 200);
            } else {
                return response()->json([
                    'Message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@atualizarStatusFatura - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'Message' => 'Erro interno do servidor'
            ], 500);
        }
    }
}
