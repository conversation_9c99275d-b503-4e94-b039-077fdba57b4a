<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Traits\HasLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Controllers\Controller;
// Requests
use Domain\MinisterioSaude\Requests\FaturaFarmamet\InserirStatusFaturaRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarStatusOpRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarEnderecoRequest;
use Domain\MinisterioSaude\Requests\ConsultarFaturasRequest;
use Domain\MinisterioSaude\Requests\AtualizarStatusFaturaRequest;
// Inputs
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\ConsultarStatusInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\CriarStatusInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input\ConsultaEnderecoInput;
// Services
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\StatusOperadorService;
use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService;
use Domain\MinisterioSaude\Services\FaturaGsnetService;

class FaturaFarmanetController extends Controller
{
    use HasLog;

    private StatusOperadorService $statusService;
    private EnderecoLocalService $enderecoService;

    public function __construct(
        StatusOperadorService $statusService,
        EnderecoLocalService $enderecoService
    ) {
        $this->statusService = $statusService;
        $this->enderecoService = $enderecoService;
    }

    /**
     * API 1.1 - Inserir Status na Fatura
     *
     * @param InserirStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $result = $this->statusService->criarStatus(CriarStatusInput::fromArray($data));

            return $this->reponseConditional($result['success'], $result['message']);
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@inserirStatusFatura - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 1.2 - Consultar Status do Operador
     *
     * @param ConsultarStatusOpRequest $request
     * @return JsonResponse
     */
    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
    {
        try {
            $result = $this->statusService->consultarStatus(ConsultarStatusInput::fromArray($request->validated()));
            return $this->reponseConditional($result['success'], $result['message'] ?? null, $result['data'] ?? null);
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@consultarStatusOp - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 1.3 - Consultar Endereço Local
     *
     * @param ConsultarEnderecoRequest $request
     * @return JsonResponse
     */
    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
    {
        try {
            $result = $this->enderecoService->consultarEndereco(ConsultaEnderecoInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@consultarEndereco - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 2.1 - Consultar Faturas (Recebe)
     *
     * @param ConsultarFaturasRequest $request
     * @return JsonResponse
     */
    public function consultarFaturas(ConsultarFaturasRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $faturaService = app(FaturaGsnetService::class);
            $result = $faturaService->consultarFaturas($data);

            return response()->json($result, 200);

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@consultarFaturas - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseError();
        }
    }

    /**
     * API 1.4 - Consultar Itens do Sistema GSNET
     *
     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function consultarItens(Request $request): JsonResponse
    {
        try {
            dd($request);
            // Validação dos dados de entrada
            $validator = Validator::make($request->all(), [
                'Data' => 'required|array',
                'Data.ListaItens' => 'required|array|min:1|max:100',
                'Data.ListaItens.*.CodigoMaterial' => 'required|numeric|digits_between:1,11',
                'AccessToken' => 'required|string|max:240'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos fornecidos.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validatedData = $validator->validated();

            // Chamar o serviço para consultar os itens
            $result = $this->consultarItensService->consultarItens($validatedData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data'],
                    'message' => $result['message'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'timestamp' => $result['timestamp']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * API 2.2 - Atualizar Status da Fatura (Envio)
     *
     * @param AtualizarStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function atualizarStatusFatura(AtualizarStatusFaturaRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $faturaService = app(\Domain\MinisterioSaude\Services\FaturaGsnetService::class);
            $result = $faturaService->atualizarStatusFatura($data);

            if ($result['success']) {
                return response()->json([
                    'Message' => $result['message']
                ], 200);
            } else {
                return response()->json([
                    'Message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('FaturaFarmanetController@atualizarStatusFatura - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseError();
        }
    }

    private function responseError($message = 'Erro interno do servidor', $statusCode = 500)
    {
        return response()->json([
            'Message' => $message
        ], $statusCode);
    }

    private function reponseConditional(bool $success, ?string $message = null, ?array $data = null)
    {
        $response = array_merge(
            $message !== null ? ['message' => $message] : [],
            $data !== null ? ['data' => $data] : []
        );

        return response()->json($response, $success ? 200 : 400);
    }
}
