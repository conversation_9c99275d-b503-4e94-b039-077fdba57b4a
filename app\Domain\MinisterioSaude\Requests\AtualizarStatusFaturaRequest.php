<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AtualizarStatusFaturaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $requestData = $this->all();
        
        // Detectar formato da requisição
        if (isset($requestData['Data'])) {
            // Formato original: {"Data": {...}}
            return [
                'Data' => 'required|array',
                'Data.ProtocoloIdGsnet' => 'required|string|max:40',
                'Data.StatusCodigo' => 'required|string|max:10',
                'Data.StatusDescricao' => 'required|string|max:100',
                'Data.Observacao' => 'nullable|string',
                'Data.DataStatus' => 'required|date_format:Y-m-d H:i:s',
                'AccessToken' => 'nullable|string|max:240'
            ];
        } else {
            // Formato novo: {"id_gestor": "...", "numero_fatura": "...", etc}
            return [
                'id_gestor' => 'required|string|max:50',
                'numero_fatura' => 'required|string|max:100',
                'status_fatura' => 'required|integer|min:1|max:10',
                'data_atualizacao' => 'required|date_format:Y-m-d H:i:s',
                'access_token' => 'required|string|max:240',
                'observacoes' => 'nullable|string|max:500'
            ];
        }
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        $requestData = $this->all();
        
        if (isset($requestData['Data'])) {
            // Mensagens para formato original
            return [
                'Data.required' => 'Os dados são obrigatórios',
                'Data.array' => 'Os dados devem estar em formato de array',
                'Data.ProtocoloIdGsnet.required' => 'O protocolo ID GSNET é obrigatório',
                'Data.ProtocoloIdGsnet.string' => 'O protocolo ID GSNET deve ser uma string',
                'Data.ProtocoloIdGsnet.max' => 'O protocolo ID GSNET não pode ter mais de 40 caracteres',
                'Data.StatusCodigo.required' => 'O código do status é obrigatório',
                'Data.StatusCodigo.string' => 'O código do status deve ser uma string',
                'Data.StatusCodigo.max' => 'O código do status não pode ter mais de 10 caracteres',
                'Data.StatusDescricao.required' => 'A descrição do status é obrigatória',
                'Data.StatusDescricao.string' => 'A descrição do status deve ser uma string',
                'Data.StatusDescricao.max' => 'A descrição do status não pode ter mais de 100 caracteres',
                'Data.Observacao.string' => 'A observação deve ser uma string',
                'Data.DataStatus.required' => 'A data do status é obrigatória',
                'Data.DataStatus.date_format' => 'A data do status deve estar no formato Y-m-d H:i:s',
                'AccessToken.max' => 'O token de acesso deve ter no máximo 240 caracteres'
            ];
        } else {
            // Mensagens para formato novo
            return [
                'id_gestor.required' => 'ID do gestor é obrigatório',
                'id_gestor.max' => 'ID do gestor deve ter no máximo 50 caracteres',
                'numero_fatura.required' => 'Número da fatura é obrigatório',
                'numero_fatura.max' => 'Número da fatura deve ter no máximo 100 caracteres',
                'status_fatura.required' => 'Status da fatura é obrigatório',
                'status_fatura.integer' => 'Status da fatura deve ser um número inteiro',
                'status_fatura.min' => 'Status da fatura deve ser maior que 0',
                'status_fatura.max' => 'Status da fatura deve ser menor ou igual a 10',
                'data_atualizacao.required' => 'Data de atualização é obrigatória',
                'data_atualizacao.date_format' => 'Data de atualização deve estar no formato Y-m-d H:i:s',
                'access_token.required' => 'Token de acesso é obrigatório',
                'access_token.max' => 'Token de acesso deve ter no máximo 240 caracteres',
                'observacoes.max' => 'Observações devem ter no máximo 500 caracteres'
            ];
        }
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        $response = response()->json([
            'success' => false,
            'message' => 'Dados de atualização inválidos',
            'errors' => $validator->errors()
        ], 422);

        throw new \Illuminate\Validation\ValidationException($validator, $response);
    }
}
