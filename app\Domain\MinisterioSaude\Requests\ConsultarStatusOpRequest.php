<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ConsultarStatusOpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'IdStatus' => 'nullable|string|max:40',
            'IdOrigem' => 'nullable|string|max:40', 
            'NomeStatus' => 'nullable|string|max:40'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'IdStatus.max' => 'IdStatus deve ter no máximo 40 caracteres',
            'IdOrigem.max' => 'IdOrigem deve ter no máximo 40 caracteres',
            'NomeStatus.max' => 'NomeStatus deve ter no máximo 40 caracteres'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'AccessToken' => 'Token de Acesso',
            'IdStatus' => 'ID do Status',
            'IdOrigem' => 'ID de Origem',
            'NomeStatus' => 'Nome do Status'
        ];
    }
}
